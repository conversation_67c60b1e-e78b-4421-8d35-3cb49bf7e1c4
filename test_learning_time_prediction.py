#!/usr/bin/env python3
"""
Test script for the Learning Time Prediction system.

This script tests the complete flow:
1. Django model creation and serialization
2. FastAPI endpoint functionality
3. Integration between Django and FastAPI

Run this script from the project root directory.
"""

import os
import sys
import django
from django.conf import settings

# Add the core directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from documents.models import Document, DocumentLearningTime
from documents.serializers import DocumentLearningTimeSerializer
from users.models import Student


def test_django_model():
    """Test the DocumentLearningTime Django model"""
    print("Testing Django DocumentLearningTime model...")
    
    try:
        # Create a test student if it doesn't exist (Student is the custom user model)
        student, created = Student.objects.get_or_create(
            username='test_user_learning_time',
            defaults={
                'email': '<EMAIL>',
                'phone_number': '1234567890'
            }
        )
        
        # Create a test document if it doesn't exist
        document, created = Document.objects.get_or_create(
            user=student,
            title='Test Document for Learning Time',
            defaults={
                'file': 'test_document.pdf',
                'processing_status': 'completed'
            }
        )
        
        # Create a learning time prediction
        learning_time, created = DocumentLearningTime.objects.update_or_create(
            document=document,
            defaults={
                'predicted_time_seconds': 3600,  # 1 hour
                'topic_difficulty': 3,  # Medium
                'content_length_words': 2500,
                'concept_density': 4,  # Medium-High
                'analysis_factors': {
                    'subject_area': 'mathematics',
                    'formula_count': 15,
                    'example_problems': 8
                },
                'gemini_reasoning': 'This is a test document with moderate complexity requiring about 1 hour to learn.'
            }
        )
        
        print(f"✓ Created/Updated learning time prediction: {learning_time}")
        print(f"  - Predicted time: {learning_time.get_time_range_display()}")
        print(f"  - Difficulty: {learning_time.get_topic_difficulty_display()}")
        print(f"  - Concept density: {learning_time.get_concept_density_display()}")
        
        # Test serialization
        serializer = DocumentLearningTimeSerializer(learning_time)
        serialized_data = serializer.data
        print(f"✓ Serialization successful: {len(serialized_data)} fields")
        
        return True
        
    except Exception as e:
        print(f"✗ Django model test failed: {str(e)}")
        return False


def test_api_models():
    """Test the FastAPI Pydantic models"""
    print("\nTesting FastAPI Pydantic models...")
    
    try:
        # Add the llm_api directory to the Python path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'llm_api'))
        
        from models import (
            LearningTimeAnalysis,
            LearningTimePredictionRequest,
            LearningTimePredictionResponse
        )
        
        # Test LearningTimeAnalysis
        analysis = LearningTimeAnalysis(
            topic_difficulty=3,
            content_length_words=2500,
            concept_density=4,
            additional_factors={'subject_area': 'mathematics'}
        )
        print(f"✓ LearningTimeAnalysis model created: {analysis}")
        
        # Test LearningTimePredictionRequest
        request = LearningTimePredictionRequest(
            document_id=1,
            llm_model='gemini',
            force_regenerate=False
        )
        print(f"✓ LearningTimePredictionRequest model created: {request}")
        
        # Test LearningTimePredictionResponse
        response = LearningTimePredictionResponse(
            message="Learning time predicted successfully",
            document_id=1,
            predicted_time_seconds=3600,
            time_range_display="48m - 1h 12m",
            analysis=analysis,
            gemini_reasoning="Test reasoning",
            status="completed",
            model="gemini",
            tokens=1250
        )
        print(f"✓ LearningTimePredictionResponse model created: {response}")
        
        return True
        
    except Exception as e:
        print(f"✗ FastAPI models test failed: {str(e)}")
        return False


def test_time_formatting():
    """Test time formatting utilities"""
    print("\nTesting time formatting utilities...")
    
    try:
        # Test the model's time formatting
        student = Student.objects.get(username='test_user_learning_time')
        document = Document.objects.get(user=student, title='Test Document for Learning Time')
        learning_time = DocumentLearningTime.objects.get(document=document)
        
        # Test different time values
        test_times = [30, 300, 1800, 3600, 7200, 10800]  # 30s, 5m, 30m, 1h, 2h, 3h
        
        for seconds in test_times:
            learning_time.predicted_time_seconds = seconds
            time_range = learning_time.get_time_range_display()
            print(f"  {seconds}s -> {time_range}")
        
        print("✓ Time formatting test successful")
        return True
        
    except Exception as e:
        print(f"✗ Time formatting test failed: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("=" * 60)
    print("Learning Time Prediction System Test")
    print("=" * 60)
    
    tests = [
        test_django_model,
        test_api_models,
        test_time_formatting
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The learning time prediction system is ready.")
        print("\nNext steps:")
        print("1. Start the Django server: cd core && python manage.py runserver")
        print("2. Start the FastAPI server: cd llm_api && python main.py")
        print("3. Test the endpoints using the frontend or API client")
        print("4. Upload a document and generate learning time predictions")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    print("=" * 60)


if __name__ == '__main__':
    main()
