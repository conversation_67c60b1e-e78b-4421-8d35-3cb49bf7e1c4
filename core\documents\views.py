from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.core.exceptions import PermissionDenied
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.shortcuts import get_object_or_404
from django.conf import settings
from django.http import HttpResponse, Http404
from datetime import datetime
import os
import fitz, docx
import requests
import logging
from .models import Document, DocumentEmbedding, Flashcard, Flowchart, Quiz, BlueprintTopics, DocumentGroup, Chapter, DocumentLearningTime

from .serializers import (
    DocumentSerializer, DocumentEmbeddingSerializer, FlashcardSerializer,
    FlowchartSerializer, QuizSerializer, UploadBlueprintSerializer,
    BlueprintTopicsSerializer, BlueprintTopicsDetailSerializer, DocumentGroupSerializer, ChapterSerializer,
    DocumentLearningTimeSerializer
)
from users.utils import increment_file_upload_count, get_usage_stats, UsageLimitExceeded
from .tasks import process_document_task, process_blueprint_task

# Configure logger
logger = logging.getLogger(__name__)

def direct_process_document(document, auth_token):
    """
    Directly process a document by sending it to the FastAPI server.
    This is an alternative to using Celery tasks when Redis is not available.

    Args:
        document: The Document model instance to process
        auth_token: The authentication token to use

    Returns:
        dict: The response from the FastAPI server
    """
    try:
        # Update document status
        document.processing_status = 'processing'
        document.save()

        # Ensure we have a valid auth token
        if not auth_token:
            auth_token = f"user_{document.user.id}"
            logger.warning(f"No auth token provided for document {document.id}, using placeholder")

        # Prepare the request
        url = f"{settings.FASTAPI_URL}/process-document/{document.id}"
        headers = {'Authorization': f'Bearer {auth_token}'}

        # Open the file and send it
        with open(document.file.path, 'rb') as f:
            files = {'file': f}
            response = requests.post(url, files=files, headers=headers)

            if response.status_code != 200:
                logger.error(f"FastAPI processing failed: {response.text}")
                document.processing_status = 'failed'
                document.error_message = f"FastAPI processing failed: {response.text}"
                document.save()
                return {'error': response.text}

            return response.json()

    except FileNotFoundError:
        logger.error(f"File not found for document {document.id}: {document.file.path}")
        document.processing_status = 'failed'
        document.error_message = f"File not found: {document.file.name}"
        document.save()
        return {'error': f'File not found: {document.file.name}'}

    except Exception as e:
        logger.error(f"Error processing document {document.id}: {str(e)}")
        document.processing_status = 'failed'
        document.error_message = str(e)
        document.save()
        return {'error': str(e)}

def direct_process_blueprint(document, auth_token, llm_model="gemini"):
    """
    Directly process a blueprint by sending it to the FastAPI server.
    This is an alternative to using Celery tasks when Redis is not available.

    Args:
        document: The Document model instance to process
        auth_token: The authentication token to use
        llm_model: The LLM model to use ("openai" or "gemini")

    Returns:
        dict: The response from the FastAPI server
    """
    try:
        # Skip if no blueprint
        if not document.blueprint:
            logger.warning(f"Document {document.id} has no blueprint")
            return {'error': f'Document {document.id} has no blueprint'}

        # Update status
        document.processing_status = 'processing'
        document.save()

        # Ensure we have a valid auth token
        if not auth_token:
            auth_token = f"user_{document.user.id}"
            logger.warning(f"No auth token provided for document {document.id}, using placeholder")

        # Prepare the request
        url = f"{settings.FASTAPI_URL}/process-blueprint/{document.id}"
        headers = {'Authorization': f'Bearer {auth_token}'}

        # Prepare payload
        payload = {
            'document_id': document.id,
            'user_id': document.user.id,
            'blueprint_text': document.blueprint,
            'llm_model': llm_model
        }

        # Send the request
        response = requests.post(url, json=payload, headers=headers)

        if response.status_code != 200:
            logger.error(f"FastAPI blueprint processing failed: {response.text}")
            document.processing_status = 'failed'
            document.error_message = f"FastAPI blueprint processing failed: {response.text}"
            document.save()
            return {'error': response.text}

        return response.json()

    except Exception as e:
        logger.error(f"Error processing blueprint for document {document.id}: {str(e)}")
        document.processing_status = 'failed'
        document.error_message = str(e)
        document.save()
        return {'error': str(e)}


class DocumentGroupViewSet(viewsets.ModelViewSet):
    serializer_class = DocumentGroupSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return DocumentGroup.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class DocumentViewSet(viewsets.ModelViewSet):
    serializer_class = DocumentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Document.objects.filter(user=self.request.user)
        group_id = self.request.query_params.get('group', None)
        if group_id:
            queryset = queryset.filter(group_id=group_id)
        return queryset

    def perform_create(self, serializer):
        group_id = self.request.data.get('group')
        if group_id:
            group = DocumentGroup.objects.filter(id=group_id, user=self.request.user).first()
            serializer.save(user=self.request.user, group=group)
        else:
            serializer.save(user=self.request.user)

    # by default the function name is the same as the url path
    @action(detail=True, methods=['get'])
    def processing_status(self, request, pk=None):
        """Get the processing status of a document"""
        document = self.get_object()
        return Response({
            'status': document.processing_status,
            'error_message': document.error_message,
            'num_embeddings': document.embeddings.count() if document.processing_status == 'completed' else 0
        })

    @action(detail=True, methods=['get', 'options'], url_path='file', permission_classes=[IsAuthenticated])
    def serve_file(self, request, pk=None):
        """Serve the document file for preview"""
        # Handle OPTIONS request for CORS
        if request.method == 'OPTIONS':
            response = HttpResponse()
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Authorization, Content-Type, X-Requested-With'
            return response

        document = self.get_object()

        try:
            # Check if file exists
            if not document.file or not default_storage.exists(document.file.name):
                raise Http404("File not found")

            # Open and read the file
            file_content = default_storage.open(document.file.name).read()

            # Determine content type based on file extension
            file_extension = document.title.split('.')[-1].lower()
            content_type_map = {
                'pdf': 'application/pdf',
                'doc': 'application/msword',
                'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'ppt': 'application/vnd.ms-powerpoint',
                'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'xls': 'application/vnd.ms-excel',
                'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'txt': 'text/plain',
                'rtf': 'application/rtf',
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'gif': 'image/gif',
                'bmp': 'image/bmp',
                'webp': 'image/webp',
                'svg': 'image/svg+xml',
                'mp3': 'audio/mpeg',
                'wav': 'audio/wav',
                'mp4': 'video/mp4',
                'avi': 'video/x-msvideo',
                'mov': 'video/quicktime',
            }

            content_type = content_type_map.get(file_extension, 'application/octet-stream')

            # Create response
            response = HttpResponse(file_content, content_type=content_type)

            # For office documents, suggest download instead of inline viewing
            if file_extension in ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']:
                response['Content-Disposition'] = f'attachment; filename="{document.title}"'
            else:
                response['Content-Disposition'] = f'inline; filename="{document.title}"'

            # Add comprehensive CORS headers for cross-origin requests
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Authorization, Content-Type, X-Requested-With'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition, Content-Length'

            # Add cache headers for better performance
            response['Cache-Control'] = 'public, max-age=3600'  # Cache for 1 hour

            # Add content length
            response['Content-Length'] = len(file_content)

            return response

        except Exception as e:
            logger.error(f"Error serving file for document {document.id}: {str(e)}")
            raise Http404("File not found or cannot be served")

    @action(detail=False, methods=['post'], url_path='upload')
    def upload(self, request):
        """Upload a document file and start processing"""
        if 'file' not in request.FILES:
            return Response(
                {"error": "No file provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        file = request.FILES['file']

        try:
            # Check and increment file upload count
            increment_file_upload_count(request.user)

            # Ensure media directory exists
            media_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'media')
            os.makedirs(media_dir, exist_ok=True)

            # Create unique filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{request.user.id}_{timestamp}_{file.name}"

            try:
                # Save the file
                file_path = default_storage.save(filename, ContentFile(file.read()))

                # --- GROUP HANDLING ---
                group_id = request.data.get('group')
                group = None
                if group_id:
                    group = DocumentGroup.objects.filter(id=group_id, user=request.user).first()

                # Create document record
                document = Document.objects.create(
                    user=request.user,
                    title=file.name,
                    file=file_path,
                    processing_status='pending',  # Set initial status
                    group=group
                )

                # Get updated usage stats
                usage_stats = get_usage_stats(request.user)

                # Get the token safely - handle different token formats
                auth_token = None
                if hasattr(request, 'auth') and request.auth:
                    if hasattr(request.auth, 'token'):
                        auth_token = request.auth.token
                    elif hasattr(request.auth, 'key'):
                        auth_token = request.auth.key
                    else:
                        auth_token = str(request.auth)

                # If we couldn't get a token, use a placeholder
                if not auth_token:
                    auth_token = f"user_{request.user.id}"

                # Try to use Celery task first
                try:
                    # Start the Celery task
                    process_document_task.delay(document.id, auth_token)
                    processing_method = "background task"
                except Exception as e:
                    logger.warning(f"Celery task failed, falling back to direct processing: {str(e)}")
                    # Fall back to direct processing
                    result = direct_process_document(document, auth_token)
                    processing_method = "direct request"

                    # If there was an error, return it
                    if 'error' in result:
                        return Response({
                            "message": f"File uploaded successfully, but processing failed: {result['error']}",
                            "file_path": file_path,
                            "document_id": document.id,
                            "status": "failed",
                            "error": result['error'],
                            "usage_stats": usage_stats
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                # If we get here, processing was started successfully (either via Celery or direct)
                return Response({
                    "message": f"File uploaded successfully. Processing started via {processing_method}.",
                    "file_path": file_path,
                    "document_id": document.id,
                    "status": "processing",
                    "usage_stats": usage_stats
                }, status=status.HTTP_202_ACCEPTED)

            except Exception as e:
                # Clean up in case of errors
                if default_storage.exists(filename):
                    default_storage.delete(filename)
                if 'document' in locals():
                    document.delete()
                # Log the specific error
                logger.error(f"Error in file upload: {str(e)}")
                raise

        except PermissionDenied:
            return Response(
                {"error": "Authentication required"},
                status=status.HTTP_401_UNAUTHORIZED
            )
        except UsageLimitExceeded as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            # Log the specific error
            logger.error(f"Error in document upload: {str(e)}")
            return Response(
                {"error": f"Error uploading file: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get', 'post'], url_path='blueprint')
    def blueprint(self, request, pk=None):
        """Get or upload a blueprint for a document"""
        document = self.get_object()

        # Handle GET request
        if request.method == 'GET':
            return Response({'blueprint': document.blueprint or ''}, status=status.HTTP_200_OK)

        # Handle POST request
        elif request.method == 'POST':
            serializer = UploadBlueprintSerializer(data=request.data)
            if serializer.is_valid():
                uploaded_file = serializer.validated_data['file']

                try:
                    text = self.extract_text(uploaded_file)
                    cleaned_text = self.clean_text(text)
                    document.blueprint = cleaned_text
                    document.save()

                    # Start async processing of blueprint using Celery
                    # Get the token safely - handle different token formats
                    auth_token = None
                    if hasattr(request, 'auth') and request.auth:
                        if hasattr(request.auth, 'token'):
                            auth_token = request.auth.token
                        elif hasattr(request.auth, 'key'):
                            auth_token = request.auth.key
                        else:
                            auth_token = str(request.auth)

                    # If we couldn't get a token, use a placeholder
                    if not auth_token:
                        auth_token = f"user_{request.user.id}"

                    # Try to use Celery task first
                    try:
                        # Start the Celery task
                        process_blueprint_task.delay(document.id, auth_token)
                        processing_method = "background task"
                    except Exception as e:
                        logger.warning(f"Celery task failed, falling back to direct processing: {str(e)}")
                        # Fall back to direct processing
                        result = direct_process_blueprint(document, auth_token)
                        processing_method = "direct request"

                        # If there was an error, return it
                        if 'error' in result:
                            return Response({
                                "message": f"Blueprint uploaded successfully, but processing failed: {result['error']}",
                                "document_id": document.id,
                                "status": "failed",
                                "error": result['error']
                            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    # If we get here, processing was started successfully (either via Celery or direct)
                    return Response({
                        'message': f'Blueprint uploaded successfully. Topic processing started via {processing_method}.',
                        'document_id': document.id,
                        'status': 'processing'
                    }, status=status.HTTP_202_ACCEPTED)
                except Exception as e:
                    return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], url_path='process-blueprint')
    def process_blueprint(self, request, pk=None):
        """
        Process an existing blueprint to extract topics

        Optional request data:
        - llm_model: The LLM model to use ("openai" or "gemini")
        """
        document = self.get_object()

        if not document.blueprint:
            return Response(
                {"error": "Document has no blueprint to process"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the LLM model from the request data, default to "openai"
        llm_model = request.data.get('llm_model', 'gemini')

        # Validate the LLM model
        if llm_model not in ['openai', 'gemini']:
            return Response(
                {"error": "Invalid LLM model. Use 'openai' or 'gemini'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Start async processing of blueprint using Celery
            # Get the token safely - handle different token formats
            auth_token = None
            if hasattr(request, 'auth') and request.auth:
                if hasattr(request.auth, 'token'):
                    auth_token = request.auth.token
                elif hasattr(request.auth, 'key'):
                    auth_token = request.auth.key
                else:
                    auth_token = str(request.auth)

            # If we couldn't get a token, use a placeholder
            if not auth_token:
                auth_token = f"user_{request.user.id}"

            # Try to use Celery task first
            try:
                # Start the Celery task
                process_blueprint_task.delay(document.id, auth_token, llm_model)
                processing_method = "background task"
            except Exception as e:
                logger.warning(f"Celery task failed, falling back to direct processing: {str(e)}")
                # Fall back to direct processing
                result = direct_process_blueprint(document, auth_token, llm_model)
                processing_method = "direct request"

                # If there was an error, return it
                if 'error' in result:
                    return Response({
                        "message": f"Blueprint processing request received, but processing failed: {result['error']}",
                        "document_id": document.id,
                        "status": "failed",
                        "error": result['error'],
                        "llm_model": llm_model
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # If we get here, processing was started successfully (either via Celery or direct)
            return Response({
                'message': f'Blueprint processing started via {processing_method} using {llm_model}.',
                'document_id': document.id,
                'status': 'processing',
                'llm_model': llm_model
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            # Log the specific error
            logger.error(f"Error starting blueprint processing for document {document.id}: {str(e)}")
            return Response(
                {"error": f"Error starting blueprint processing: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def extract_text(self, file):
        """Extract text from various file formats"""
        # PDF files
        if file.name.endswith('.pdf'):
            doc = fitz.open(stream=file.read(), filetype="pdf")
            return "\n".join([page.get_text() for page in doc])

        # DOCX files
        elif file.name.endswith('.docx'):
            return "\n".join([p.text for p in docx.Document(file).paragraphs])

        # TXT files
        elif file.name.endswith('.txt'):
            return file.read().decode('utf-8')

        else:
            raise ValueError("Unsupported file type.")

    def clean_text(self, text):
        """Clean extracted text"""
        return " ".join(text.split())

class DocumentEmbeddingViewSet(viewsets.ModelViewSet):
    serializer_class = DocumentEmbeddingSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return DocumentEmbedding.objects.filter(document__user=self.request.user)

class FlashcardViewSet(viewsets.ModelViewSet):
    serializer_class = FlashcardSerializer
    permission_classes = [IsAuthenticated]

    def generate_flashcards_for_document(self, document, auth_token=None, llm_model="gemini", num_flashcards=10):
        """
        Generate flashcards for a document using the FastAPI endpoint.

        Args:
            document: The Document model instance
            auth_token: Authentication token to use
            llm_model: The LLM model to use ("openai" or "gemini")
            num_flashcards: Number of flashcards to generate

        Returns:
            list: List of created Flashcard objects or None if generation failed
        """
        try:
            # Ensure we have a valid auth token
            if not auth_token:
                auth_token = f"user_{document.user.id}"
                logger.warning(f"No auth token provided for document {document.id}, using placeholder")

            # Prepare the request
            url = f"{settings.FASTAPI_URL}/generate-flashcards/{document.id}"
            headers = {'Authorization': f'Bearer {auth_token}'}

            # Log request details
            logger.info(f"Making request to FastAPI: URL={url}, document_id={document.id}, llm_model={llm_model}")
            
            # Prepare payload
            payload = {
                'document_id': document.id,
                'llm_model': llm_model,
                'num_flashcards': num_flashcards
            }

            # Send the request
            logger.info(f"Sending request to FastAPI with payload: {payload}")
            response = requests.post(url, json=payload, headers=headers)
            logger.info(f"FastAPI response status: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"FastAPI flashcard generation failed: Status={response.status_code}, Response={response.text}")
                return None

            # Parse the response
            flashcard_data = response.json()
            logger.info(f"Received flashcard data: {flashcard_data}")
            created_flashcards = []

            # Create Flashcard objects from the response
            if 'flashcards' in flashcard_data and flashcard_data['status'] == 'completed':
                for card_data in flashcard_data['flashcards']:
                    logger.info(f"Creating flashcard: front={card_data['front'][:50]}...")
                    flashcard = Flashcard.objects.create(
                        document=document,
                        front=card_data['front'],
                        back=card_data['back']
                    )
                    created_flashcards.append(flashcard)

                logger.info(f"Generated {len(created_flashcards)} flashcards for document {document.id}")
                return created_flashcards
            else:
                logger.error(f"Invalid response format from FastAPI: {flashcard_data}")
                return None

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error to FastAPI service: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error generating flashcards for document {document.id}: {str(e)}")
            return None

    def get_queryset(self):
        queryset = Flashcard.objects.filter(document__user=self.request.user)
        logger.info("Initial queryset count: %d", queryset.count())

        # Get document_id from URL path parameters
        document_id = self.kwargs.get('document_id')
        if document_id:
            logger.info("Processing document_id: %s", document_id)
            queryset = queryset.filter(document_id=document_id)
            logger.info("Filtered queryset count: %d", queryset.count())

            # If no flashcards exist for this document and it's a GET request, generate them
            if not queryset.exists() and self.request.method == 'GET':
                logger.info("No flashcards exist, attempting to generate")
                try:
                    document = Document.objects.get(id=document_id, user=self.request.user)
                    logger.info("Found document: %s", document.title)

                    # Check if document has embeddings (required for flashcard generation)
                    if document.embeddings.exists():
                        logger.info("Document has embeddings, proceeding with flashcard generation")
                        # Get the token safely - handle different token formats
                        auth_token = None
                        if hasattr(self.request, 'auth') and self.request.auth:
                            if hasattr(self.request.auth, 'token'):
                                auth_token = self.request.auth.token
                            elif hasattr(self.request.auth, 'key'):
                                auth_token = self.request.auth.key
                            else:
                                auth_token = str(self.request.auth)
                        logger.info("Using auth token: %s", auth_token[:10] if auth_token else None)

                        # Generate flashcards
                        created_flashcards = self.generate_flashcards_for_document(document, auth_token)

                        # If flashcards were created, update the queryset
                        if created_flashcards:
                            logger.info("Successfully created %d flashcards", len(created_flashcards))
                            queryset = Flashcard.objects.filter(document_id=document_id, document__user=self.request.user)
                        else:
                            logger.error("Failed to create flashcards")
                    else:
                        logger.error("Document %s has no embeddings", document_id)
                except Document.DoesNotExist:
                    logger.error("Document %s not found", document_id)
                except Exception as e:
                    logger.error("Unexpected error: %s", str(e))
        return queryset

    @action(detail=False, methods=['post'])
    def generate(self, request):
        document_id = request.data.get('document_id')
        if not document_id:
            return Response({"error": "document_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        llm_model = request.data.get('llm_model', 'gemini')
        num_flashcards = request.data.get('num_flashcards', 10)
        force = request.data.get('force', False)

        try:
            document = Document.objects.get(id=document_id, user=request.user)
            if not document.embeddings.exists():
                return Response(
                    {"error": "Document has no embeddings. Process the document first."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            existing_flashcards = Flashcard.objects.filter(document=document)
            if existing_flashcards.exists() and force:
                existing_flashcards.delete()
            elif existing_flashcards.exists():
                serializer = self.get_serializer(existing_flashcards, many=True)
                return Response({
                    "message": "Flashcards already exist for this document",
                    "flashcards": serializer.data
                })

            auth_token = self._get_auth_token()
            created_flashcards = self.generate_flashcards_for_document(
                document,
                auth_token,
                llm_model=llm_model,
                num_flashcards=num_flashcards
            )

            if created_flashcards:
                serializer = self.get_serializer(created_flashcards, many=True)
                return Response({
                    "message": "Flashcards generated successfully",
                    "flashcards": serializer.data
                })
            return Response(
                {"error": "Failed to generate flashcards"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        except Document.DoesNotExist:
            return Response(
                {"error": "Document not found or you don't have permission to access it"},
                status=status.HTTP_404_NOT_FOUND
            )

    def _get_auth_token(self):
        """Helper method to safely extract auth token from request"""
        if hasattr(self.request, 'auth') and self.request.auth:
            if hasattr(self.request.auth, 'token'):
                return self.request.auth.token
            elif hasattr(self.request.auth, 'key'):
                return self.request.auth.key
            return str(self.request.auth)
        return None

class FlowchartViewSet(viewsets.ModelViewSet):
    serializer_class = FlowchartSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Flowchart.objects.filter(document__user=self.request.user)
        document_id = self.kwargs.get('document_id') or self.request.query_params.get('document', None)
        if document_id:
            queryset = queryset.filter(document_id=document_id)
            if not queryset.exists() and self.request.method == 'GET':
                try:
                    document = Document.objects.get(id=document_id, user=self.request.user)
                    if document.embeddings.exists():
                        auth_token = self._get_auth_token()
                        created_flowchart = self.generate_flowchart_for_document(document, auth_token)
                        if created_flowchart:
                            queryset = Flowchart.objects.filter(document_id=document_id, document__user=self.request.user)
                except Document.DoesNotExist:
                    pass
        return queryset

    def generate_flowchart_for_document(self, document, auth_token=None, llm_model="gemini"):
        """
        Generate flowchart for a document using the FastAPI endpoint.
        """
        try:
            if not auth_token:
                auth_token = f"user_{document.user.id}"
                logger.warning(f"No auth token provided for document {document.id}, using placeholder")

            url = f"{settings.FASTAPI_URL}/generate-flowchart/{document.id}"
            headers = {'Authorization': f'Bearer {auth_token}'}
            payload = {
                'document_id': document.id,
                'llm_model': llm_model
            }

            response = requests.post(url, json=payload, headers=headers)
            if response.status_code != 200:
                logger.error(f"FastAPI flowchart generation failed: {response.text}")
                return None

            flowchart_data = response.json()
            if flowchart_data.get('status') == 'completed' and 'flowchart' in flowchart_data:
                flowchart = Flowchart.objects.create(
                    document=document,
                    mermaid_code=flowchart_data['flowchart']
                )
                logger.info(f"Generated flowchart for document {document.id}")
                return flowchart
            else:
                logger.error(f"Invalid response format from FastAPI: {flowchart_data}")
                return None

        except Exception as e:
            logger.error(f"Error generating flowchart for document {document.id}: {str(e)}")
            return None

    @action(detail=False, methods=['post'])
    def generate(self, request):
        document_id = request.data.get('document_id')
        if not document_id:
            return Response({"error": "document_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        llm_model = request.data.get('llm_model', 'gemini')
        force = request.data.get('force', False)

        try:
            document = Document.objects.get(id=document_id, user=request.user)
            if not document.embeddings.exists():
                return Response(
                    {"error": "Document has no embeddings. Process the document first."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            existing_flowchart = Flowchart.objects.filter(document=document)
            if existing_flowchart.exists() and force:
                existing_flowchart.delete()
            elif existing_flowchart.exists():
                serializer = self.get_serializer(existing_flowchart.first())
                return Response({
                    "message": "Flowchart already exists for this document",
                    "flowchart": serializer.data
                })

            auth_token = self._get_auth_token()
            created_flowchart = self.generate_flowchart_for_document(
                document,
                auth_token,
                llm_model=llm_model
            )

            if created_flowchart:
                serializer = self.get_serializer(created_flowchart)
                return Response({
                    "message": "Flowchart generated successfully",
                    "flowchart": serializer.data
                })
            return Response(
                {"error": "Failed to generate flowchart"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        except Document.DoesNotExist:
            return Response(
                {"error": "Document not found or you don't have permission to access it"},
                status=status.HTTP_404_NOT_FOUND
            )

    def _get_auth_token(self):
        """Helper method to safely extract auth token from request"""
        if hasattr(self.request, 'auth') and self.request.auth:
            if hasattr(self.request.auth, 'token'):
                return self.request.auth.token
            elif hasattr(self.request.auth, 'key'):
                return self.request.auth.key
            return str(self.request.auth)
        return None

class QuizViewSet(viewsets.ModelViewSet):
    serializer_class = QuizSerializer
    permission_classes = [IsAuthenticated]

    def generate_quiz_for_document(self, document, auth_token=None, llm_model="gemini", num_questions=5):
        """
        Generate quiz questions and answers for a document using the FastAPI endpoint.

        Args:
            document: The Document model instance
            auth_token: Authentication token to use
            llm_model: The LLM model to use ("openai" or "gemini")
            num_questions: Number of questions to generate

        Returns:
            list: List of created Quiz objects or None if generation failed
        """
        try:
            # Ensure we have a valid auth token
            if not auth_token:
                auth_token = f"user_{document.user.id}"
                logger.warning(f"No auth token provided for document {document.id}, using placeholder")

            # Prepare the request
            url = f"{settings.FASTAPI_URL}/generate-quiz/{document.id}"
            headers = {'Authorization': f'Bearer {auth_token}'}

            # Prepare payload
            payload = {
                'document_id': document.id,
                'llm_model': llm_model,
                'num_questions': num_questions
            }

            # Send the request
            response = requests.post(url, json=payload, headers=headers)

            if response.status_code != 200:
                logger.error(f"FastAPI quiz generation failed: {response.text}")
                return None

            # Parse the response
            quiz_data = response.json()
            created_quizzes = []

            # Create Quiz objects from the response
            if 'questions' in quiz_data and quiz_data['status'] == 'completed':
                for question_data in quiz_data['questions']:
                    quiz = Quiz.objects.create(
                        document=document,
                        question=question_data['question'],
                        answer=question_data['answer']
                    )
                    created_quizzes.append(quiz)

                logger.info(f"Generated {len(created_quizzes)} quiz questions for document {document.id}")
                return created_quizzes
            else:
                logger.error(f"Invalid response format from FastAPI: {quiz_data}")
                return None

        except Exception as e:
            logger.error(f"Error generating quiz for document {document.id}: {str(e)}")
            return None

    def get_queryset(self):
        queryset = Quiz.objects.filter(document__user=self.request.user)

        # Check if we need to filter by document
        document_id = self.request.query_params.get('document', None)
        if document_id:
            queryset = queryset.filter(document_id=document_id)

            # If no quizzes exist for this document, generate them
            if not queryset.exists():
                try:
                    document = Document.objects.get(id=document_id, user=self.request.user)

                    # Check if document has embeddings (required for quiz generation)
                    if document.embeddings.exists():
                        # Get the token safely - handle different token formats
                        auth_token = None
                        if hasattr(self.request, 'auth') and self.request.auth:
                            if hasattr(self.request.auth, 'token'):
                                auth_token = self.request.auth.token
                            elif hasattr(self.request.auth, 'key'):
                                auth_token = self.request.auth.key
                            else:
                                auth_token = str(self.request.auth)

                        # Generate quizzes
                        created_quizzes = self.generate_quiz_for_document(document, auth_token)

                        # If quizzes were created, update the queryset
                        if created_quizzes:
                            queryset = Quiz.objects.filter(document_id=document_id, document__user=self.request.user)
                except Document.DoesNotExist:
                    logger.warning(f"Document {document_id} not found or does not belong to user {self.request.user.id}")

        return queryset

    def document_quiz(self, request, document_id=None):
        """
        Combined GET/POST endpoint for quiz management by document ID.

        GET: Retrieve existing quizzes for a document, generate if none exist
        POST: Force generation of new quizzes for a document

        URL: /api/documents/{document_id}/quiz/

        Optional parameters for POST:
        - llm_model: The LLM model to use ("openai" or "gemini")
        - num_questions: Number of questions to generate (default: 5)
        """
        try:
            # Get the document
            try:
                document = Document.objects.get(id=document_id, user=request.user)
            except Document.DoesNotExist:
                return Response(
                    {"error": "Document not found or you don't have permission to access it"},
                    status=status.HTTP_404_NOT_FOUND
                )

            if request.method == 'GET':
                # Always generate new quizzes for each file (RAG-based per file)
                # First, delete any existing quizzes for this document
                existing_quizzes = Quiz.objects.filter(document=document)
                if existing_quizzes.exists():
                    existing_quizzes.delete()
                    logger.info(f"Deleted {existing_quizzes.count()} existing quizzes for document {document.id}")

                # Check if document has embeddings
                if not document.embeddings.exists():
                    return Response(
                        {"error": "Document has no embeddings. Process the document first."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Get the token safely
                auth_token = self._get_auth_token(request)

                # Generate new quizzes with default parameters
                created_quizzes = self.generate_quiz_for_document(
                    document,
                    auth_token,
                    llm_model="gemini",
                    num_questions=5
                )

                if created_quizzes:
                    serializer = QuizSerializer(created_quizzes, many=True)
                    return Response({
                        "message": "Quizzes generated successfully",
                        "document_id": document.id,
                        "quizzes": serializer.data,
                        "quiz_count": len(created_quizzes),
                        "generated": True
                    })
                else:
                    return Response(
                        {"error": "Failed to generate quiz. Check server logs for details."},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            elif request.method == 'POST':
                # Force generation of new quizzes
                # Always delete existing quizzes first (RAG-based per file)
                existing_quizzes = Quiz.objects.filter(document=document)
                if existing_quizzes.exists():
                    existing_quizzes.delete()
                    logger.info(f"Deleted {existing_quizzes.count()} existing quizzes for document {document.id}")

                # Get optional parameters
                llm_model = request.data.get('llm_model', 'gemini')
                num_questions = request.data.get('num_questions', 5)

                # Validate parameters
                try:
                    num_questions = int(num_questions)
                    if num_questions <= 0 or num_questions > 20:
                        return Response(
                            {"error": "num_questions must be between 1 and 20"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except (ValueError, TypeError):
                    return Response(
                        {"error": "num_questions must be a valid integer"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                if llm_model not in ['openai', 'gemini']:
                    return Response(
                        {"error": "llm_model must be 'openai' or 'gemini'"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Check if document has embeddings
                if not document.embeddings.exists():
                    return Response(
                        {"error": "Document has no embeddings. Process the document first."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Delete existing quizzes if any
                existing_quizzes = Quiz.objects.filter(document=document)
                if existing_quizzes.exists():
                    existing_quizzes.delete()

                # Get the token safely
                auth_token = self._get_auth_token(request)

                # Generate new quizzes
                created_quizzes = self.generate_quiz_for_document(
                    document,
                    auth_token,
                    llm_model=llm_model,
                    num_questions=num_questions
                )

                if created_quizzes:
                    serializer = QuizSerializer(created_quizzes, many=True)
                    return Response({
                        "message": "Quizzes generated successfully",
                        "document_id": document.id,
                        "quizzes": serializer.data,
                        "quiz_count": len(created_quizzes),
                        "generated": True
                    })
                else:
                    return Response(
                        {"error": "Failed to generate quiz. Check server logs for details."},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

        except Exception as e:
            logger.error(f"Error in quiz endpoint: {str(e)}")
            return Response(
                {"error": f"Error processing quiz request: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_auth_token(self, request):
        """Helper method to safely extract auth token from request"""
        auth_token = None
        if hasattr(request, 'auth') and request.auth:
            if hasattr(request.auth, 'token'):
                auth_token = request.auth.token
            elif hasattr(request.auth, 'key'):
                auth_token = request.auth.key
            else:
                auth_token = str(request.auth)
        return auth_token

    @action(detail=False, methods=['post'], url_path='generate')
    def generate_quiz(self, request):
        """
        DEPRECATED: Use the quiz_endpoint instead.
        Generate quiz questions for a document.

        Required parameters:
        - document_id: ID of the document to generate quiz for

        Optional parameters:
        - llm_model: The LLM model to use ("openai" or "gemini")
        - num_questions: Number of questions to generate (default: 5)
        - force: Whether to force regeneration if quizzes already exist (default: false)
        """
        # Get required parameters
        document_id = request.data.get('document_id')
        if not document_id:
            return Response(
                {"error": "document_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get optional parameters
        llm_model = request.data.get('llm_model', 'gemini')
        num_questions = request.data.get('num_questions', 5)
        force = request.data.get('force', False)

        # Validate parameters
        try:
            num_questions = int(num_questions)
            if num_questions <= 0 or num_questions > 20:
                return Response(
                    {"error": "num_questions must be between 1 and 20"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {"error": "num_questions must be a valid integer"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if llm_model not in ['openai', 'gemini']:
            return Response(
                {"error": "llm_model must be 'openai' or 'gemini'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get the document
            document = Document.objects.get(id=document_id, user=request.user)

            # Check if document has embeddings
            if not document.embeddings.exists():
                return Response(
                    {"error": "Document has no embeddings. Process the document first."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if quizzes already exist for this document
            existing_quizzes = Quiz.objects.filter(document=document)
            if existing_quizzes.exists() and not force:
                return Response({
                    "message": f"Quizzes already exist for this document. Use force=true to regenerate.",
                    "document_id": document_id,
                    "quiz_count": existing_quizzes.count()
                })

            # If force is true, delete existing quizzes
            if existing_quizzes.exists() and force:
                existing_quizzes.delete()

            # Get the token safely - handle different token formats
            auth_token = None
            if hasattr(request, 'auth') and request.auth:
                if hasattr(request.auth, 'token'):
                    auth_token = request.auth.token
                elif hasattr(request.auth, 'key'):
                    auth_token = request.auth.key
                else:
                    auth_token = str(request.auth)

            # Generate quizzes
            created_quizzes = self.generate_quiz_for_document(
                document,
                auth_token,
                llm_model=llm_model,
                num_questions=num_questions
            )

            if created_quizzes:
                return Response({
                    "message": "Quiz generated successfully",
                    "document_id": document_id,
                    "quiz_count": len(created_quizzes)
                })
            else:
                return Response(
                    {"error": "Failed to generate quiz. Check server logs for details."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        except Document.DoesNotExist:
            return Response(
                {"error": "Document not found or you don't have permission to access it"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error generating quiz: {str(e)}")
            return Response(
                {"error": f"Error generating quiz: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ChapterViewSet(viewsets.ModelViewSet):
    serializer_class = ChapterSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter chapters by document and user"""
        document_id = self.kwargs.get('document_id')
        if document_id:
            return Chapter.objects.filter(
                document_id=document_id,
                document__user=self.request.user
            ).order_by('order')
        return Chapter.objects.none()

    def generate_chapters_for_document(self, document, auth_token=None, llm_model="gemini", num_chapters=5):
        """
        Generate chapters for a document using the FastAPI endpoint.
        """
        try:
            if not auth_token:
                auth_token = f"user_{document.user.id}"
                logger.warning(f"No auth token provided for document {document.id}, using placeholder")

            url = f"{settings.FASTAPI_URL}/generate-chapters/{document.id}"
            headers = {'Authorization': f'Bearer {auth_token}'}
            payload = {
                'document_id': document.id,
                'llm_model': llm_model,
                'num_chapters': num_chapters
            }

            response = requests.post(url, json=payload, headers=headers)
            if response.status_code != 200:
                logger.error(f"FastAPI chapter generation failed: {response.text}")
                return None

            chapter_data = response.json()
            created_chapters = []

            if 'chapters' in chapter_data and chapter_data['status'] == 'completed':
                for i, chapter_info in enumerate(chapter_data['chapters']):
                    chapter = Chapter.objects.create(
                        document=document,
                        title=chapter_info['title'],
                        content=chapter_info['content'],
                        order=i + 1,
                        subsections=chapter_info.get('subsections', [])
                    )
                    created_chapters.append(chapter)

                logger.info(f"Generated {len(created_chapters)} chapters for document {document.id}")
                return created_chapters
            else:
                logger.error(f"Invalid response format from FastAPI: {chapter_data}")
                return None

        except Exception as e:
            logger.error(f"Error generating chapters for document {document.id}: {str(e)}")
            return None

    def document_chapters(self, request, document_id=None):
        """
        Combined GET/POST endpoint for chapter management by document ID.
        """
        try:
            try:
                document = Document.objects.get(id=document_id, user=request.user)
            except Document.DoesNotExist:
                return Response(
                    {"error": "Document not found or you don't have permission to access it"},
                    status=status.HTTP_404_NOT_FOUND
                )

            if request.method == 'GET':
                # Always generate new chapters for each file (RAG-based per file)
                existing_chapters = Chapter.objects.filter(document=document)
                if existing_chapters.exists():
                    existing_chapters.delete()
                    logger.info(f"Deleted {existing_chapters.count()} existing chapters for document {document.id}")

                if not document.embeddings.exists():
                    return Response(
                        {"error": "Document has no embeddings. Process the document first."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                auth_token = self._get_auth_token(request)
                created_chapters = self.generate_chapters_for_document(
                    document,
                    auth_token,
                    llm_model="gemini",
                    num_chapters=5
                )

                if created_chapters:
                    serializer = ChapterSerializer(created_chapters, many=True)
                    return Response({
                        "message": "Chapters generated successfully",
                        "document_id": document.id,
                        "chapters": serializer.data,
                        "chapter_count": len(created_chapters),
                        "generated": True
                    })
                else:
                    return Response(
                        {"error": "Failed to generate chapters. Check server logs for details."},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            elif request.method == 'POST':
                # Force generation of new chapters
                existing_chapters = Chapter.objects.filter(document=document)
                if existing_chapters.exists():
                    existing_chapters.delete()
                    logger.info(f"Deleted {existing_chapters.count()} existing chapters for document {document.id}")

                llm_model = request.data.get('llm_model', 'gemini')
                num_chapters = request.data.get('num_chapters', 5)

                try:
                    num_chapters = int(num_chapters)
                    if num_chapters <= 0 or num_chapters > 10:
                        return Response(
                            {"error": "num_chapters must be between 1 and 10"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except (ValueError, TypeError):
                    return Response(
                        {"error": "num_chapters must be a valid integer"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                if llm_model not in ['openai', 'gemini']:
                    return Response(
                        {"error": "llm_model must be 'openai' or 'gemini'"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                if not document.embeddings.exists():
                    return Response(
                        {"error": "Document has no embeddings. Process the document first."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                auth_token = self._get_auth_token(request)
                created_chapters = self.generate_chapters_for_document(
                    document,
                    auth_token,
                    llm_model=llm_model,
                    num_chapters=num_chapters
                )

                if created_chapters:
                    serializer = ChapterSerializer(created_chapters, many=True)
                    return Response({
                        "message": "Chapters generated successfully",
                        "document_id": document.id,
                        "chapters": serializer.data,
                        "chapter_count": len(created_chapters),
                        "generated": True
                    })
                else:
                    return Response(
                        {"error": "Failed to generate chapters. Check server logs for details."},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

        except Exception as e:
            logger.error(f"Unexpected error in chapter generation: {str(e)}")
            return Response(
                {"error": f"Unexpected error: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get', 'post'], url_path='learning-time')
    def learning_time(self, request, pk=None):
        """
        GET: Retrieve existing learning time prediction for a document
        POST: Generate new learning time prediction using Gemini AI
        """
        document = self.get_object()

        try:
            if request.method == 'GET':
                # Try to get existing learning time prediction
                try:
                    learning_time = DocumentLearningTime.objects.get(document=document)
                    serializer = DocumentLearningTimeSerializer(learning_time)
                    return Response(serializer.data)
                except DocumentLearningTime.DoesNotExist:
                    return Response(
                        {"error": "Learning time prediction not found. Generate one first."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            elif request.method == 'POST':
                # Generate new learning time prediction
                llm_model = request.data.get('llm_model', 'gemini')
                force_regenerate = request.data.get('force_regenerate', False)

                if llm_model not in ['openai', 'gemini']:
                    return Response(
                        {"error": "llm_model must be 'openai' or 'gemini'"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                if not document.embeddings.exists():
                    return Response(
                        {"error": "Document has no embeddings. Process the document first."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                auth_token = self._get_auth_token(request)
                prediction_result = self.generate_learning_time_for_document(
                    document,
                    auth_token,
                    llm_model=llm_model,
                    force_regenerate=force_regenerate
                )

                if prediction_result:
                    return Response({
                        "message": "Learning time predicted successfully",
                        "document_id": document.id,
                        "prediction": prediction_result,
                        "generated": True
                    })
                else:
                    return Response(
                        {"error": "Failed to generate learning time prediction. Check server logs for details."},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

        except Exception as e:
            logger.error(f"Unexpected error in learning time prediction: {str(e)}")
            return Response(
                {"error": f"Unexpected error: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'], url_path='save-learning-time')
    def save_learning_time(self, request, pk=None):
        """
        Save learning time prediction data from FastAPI.
        This endpoint is called by the FastAPI service after generating predictions.
        """
        document = self.get_object()

        try:
            # Extract data from request
            data = request.data
            required_fields = [
                'predicted_time_seconds', 'topic_difficulty', 'content_length_words',
                'concept_density', 'analysis_factors', 'gemini_reasoning'
            ]

            for field in required_fields:
                if field not in data:
                    return Response(
                        {"error": f"Missing required field: {field}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Create or update learning time prediction
            learning_time, created = DocumentLearningTime.objects.update_or_create(
                document=document,
                defaults={
                    'predicted_time_seconds': data['predicted_time_seconds'],
                    'topic_difficulty': data['topic_difficulty'],
                    'content_length_words': data['content_length_words'],
                    'concept_density': data['concept_density'],
                    'analysis_factors': data['analysis_factors'],
                    'gemini_reasoning': data['gemini_reasoning']
                }
            )

            serializer = DocumentLearningTimeSerializer(learning_time)
            return Response({
                "message": "Learning time prediction saved successfully",
                "created": created,
                "learning_time": serializer.data
            })

        except Exception as e:
            logger.error(f"Error saving learning time prediction: {str(e)}")
            return Response(
                {"error": f"Error saving learning time prediction: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def generate_learning_time_for_document(self, document, auth_token=None, llm_model="gemini", force_regenerate=False):
        """
        Generate learning time prediction for a document using the FastAPI endpoint.
        """
        try:
            if not auth_token:
                auth_token = f"user_{document.user.id}"
                logger.warning(f"No auth token provided for document {document.id}, using placeholder")

            url = f"{settings.FASTAPI_URL}/predict-learning-time/{document.id}"
            headers = {'Authorization': f'Bearer {auth_token}'}
            payload = {
                'document_id': document.id,
                'llm_model': llm_model,
                'force_regenerate': force_regenerate
            }

            response = requests.post(url, json=payload, headers=headers)
            if response.status_code != 200:
                logger.error(f"FastAPI learning time prediction failed: {response.text}")
                return None

            response_data = response.json()

            # Return the prediction data
            return {
                'predicted_time_seconds': response_data['predicted_time_seconds'],
                'time_range_display': response_data['time_range_display'],
                'analysis': response_data['analysis'],
                'gemini_reasoning': response_data['gemini_reasoning'],
                'model': response_data['model'],
                'tokens': response_data['tokens']
            }

        except Exception as e:
            logger.error(f"Error generating learning time prediction: {str(e)}")
            return None

    def _get_auth_token(self, request):
        """Helper method to extract auth token from request"""
        auth_token = None
        if hasattr(request, 'auth') and request.auth:
            if hasattr(request.auth, 'token'):
                auth_token = request.auth.token
            elif hasattr(request.auth, 'key'):
                auth_token = request.auth.key
            else:
                auth_token = str(request.auth)
        return auth_token

class BlueprintTopicsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing blueprint topics.

    list:
        Get all blueprint topics for the current user

    retrieve:
        Get a specific blueprint topic

    create:
        Create a new blueprint topic

    update:
        Update a blueprint topic

    partial_update:
        Partially update a blueprint topic

    destroy:
        Delete a blueprint topic
    """
    serializer_class = BlueprintTopicsSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'retrieve' or self.action == 'content_details':
            return BlueprintTopicsDetailSerializer
        return BlueprintTopicsSerializer

    def get_queryset(self):
        queryset = BlueprintTopics.objects.filter(document__user=self.request.user)

        # Filter by document if provided
        document_id = self.request.query_params.get('document', None)
        if document_id:
            queryset = queryset.filter(document_id=document_id)

        # Filter by minimum weightage if provided
        min_weightage = self.request.query_params.get('min_weightage', None)
        if min_weightage:
            try:
                queryset = queryset.filter(weightage__gte=float(min_weightage))
            except ValueError:
                pass

        # Search by title if provided
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(title__icontains=search)

        # Order by weightage (descending) by default
        return queryset.order_by('-weightage')

    @action(detail=True, methods=['get'])
    def content_details(self, request, pk=None):
        """Get detailed information about the content associated with this topic"""
        topic = self.get_object()
        serializer = BlueprintTopicsDetailSerializer(topic)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_document(self, request):
        """Get all topics for a specific document"""
        document_id = request.query_params.get('document_id', None)
        if not document_id:
            return Response(
                {"error": "document_id parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if document belongs to user
        try:
            document = Document.objects.get(id=document_id, user=request.user)
        except Document.DoesNotExist:
            return Response(
                {"error": "Document not found or you don't have permission to access it"},
                status=status.HTTP_404_NOT_FOUND
            )

        topics = BlueprintTopics.objects.filter(document=document).order_by('-weightage')
        serializer = self.get_serializer(topics, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def top_topics(self, request):
        """Get top topics by weightage"""
        limit = request.query_params.get('limit', 5)
        try:
            limit = int(limit)
        except ValueError:
            limit = 5

        topics = BlueprintTopics.objects.filter(
            document__user=request.user
        ).order_by('-weightage')[:limit]

        serializer = self.get_serializer(topics, many=True)
        return Response(serializer.data)