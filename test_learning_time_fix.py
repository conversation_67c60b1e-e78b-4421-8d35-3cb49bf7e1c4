#!/usr/bin/env python3
"""
Test script to verify the learning time prediction system is working correctly.
This script tests the complete workflow from document processing to learning time prediction.
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# Add the Django project to the path
core_path = str(Path(__file__).parent / 'core')
sys.path.insert(0, core_path)

# Change to core directory for Django setup
original_cwd = os.getcwd()
os.chdir(core_path)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

# Change back to original directory
os.chdir(original_cwd)

from django.contrib.auth import get_user_model
from documents.models import Document, DocumentLearningTime
from rest_framework.authtoken.models import Token

User = get_user_model()
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_fastapi_connection():
    """Test if FastAPI server is accessible"""
    try:
        response = requests.get("http://localhost:8001/docs", timeout=5)
        if response.status_code == 200:
            print("✓ FastAPI server is running and accessible")
            return True
        else:
            print(f"❌ FastAPI server returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ FastAPI server is not accessible: {str(e)}")
        return False

def test_authentication():
    """Test authentication system"""
    try:
        # Try to get an existing user first
        user = User.objects.filter(email__isnull=False).first()

        if not user:
            # Create a test user with unique email
            import uuid
            unique_email = f"test_{uuid.uuid4().hex[:8]}@example.com"
            user = User.objects.create(
                username=f'test_learning_time_{uuid.uuid4().hex[:8]}',
                email=unique_email
            )
            print(f"✓ Test user created: {user.username}")
        else:
            print(f"✓ Using existing user: {user.username}")

        # Get or create auth token
        token, created = Token.objects.get_or_create(user=user)

        print(f"✓ Auth token: {token.key[:10]}...")

        return user, token.key
    except Exception as e:
        print(f"❌ Authentication setup failed: {str(e)}")
        return None, None

def test_learning_time_prediction(user, auth_token):
    """Test learning time prediction with a real document"""
    try:
        # Find a document with embeddings
        document = Document.objects.filter(
            processing_status='completed',
            embeddings__isnull=False
        ).first()

        if not document:
            print("❌ No completed document with embeddings found for testing")
            return False
        
        print(f"✓ Using document: {document.title} (ID: {document.id})")
        
        # Test FastAPI endpoint directly
        url = f"http://localhost:8001/predict-learning-time/{document.id}"
        headers = {'Authorization': f'Bearer {auth_token}'}
        payload = {
            'document_id': document.id,
            'llm_model': 'gemini',
            'force_regenerate': True
        }
        
        print(f"Making request to: {url}")
        print(f"Headers: {headers}")
        print(f"Payload: {payload}")
        
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Learning time prediction successful!")
            print(f"  Predicted time: {result.get('time_range_display', 'N/A')}")
            print(f"  Analysis: {result.get('analysis', {})}")
            
            # Check if it was saved to database
            learning_time = DocumentLearningTime.objects.filter(document=document).first()
            if learning_time:
                print(f"✓ Learning time saved to database: {learning_time.predicted_time_seconds} seconds")
            else:
                print("⚠️ Learning time not found in database")
            
            return True
        else:
            print(f"❌ Learning time prediction failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Learning time prediction test failed: {str(e)}")
        return False

def test_celery_task(user, auth_token):
    """Test the Celery task directly"""
    try:
        from documents.tasks import generate_learning_time_task
        
        # Find a document with embeddings
        document = Document.objects.filter(
            processing_status='completed',
            embeddings__isnull=False
        ).first()

        if not document:
            print("❌ No completed document with embeddings found for Celery testing")
            return False
        
        print(f"✓ Testing Celery task with document: {document.title} (ID: {document.id})")
        
        # Clear any existing learning time predictions for this test
        DocumentLearningTime.objects.filter(document=document).delete()
        
        # Call the task directly (not through Celery)
        result = generate_learning_time_task(document.id, auth_token, "gemini")
        
        if result and 'error' not in result:
            print(f"✓ Celery task completed successfully")
            
            # Check if it was saved to database
            learning_time = DocumentLearningTime.objects.filter(document=document).first()
            if learning_time:
                print(f"✓ Learning time saved to database: {learning_time.predicted_time_seconds} seconds")
                return True
            else:
                print("⚠️ Learning time not found in database after task completion")
                return False
        else:
            print(f"❌ Celery task failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Celery task test failed: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("LEARNING TIME PREDICTION SYSTEM TEST")
    print("=" * 60)
    
    # Test 1: FastAPI Connection
    print("\n1. Testing FastAPI connection...")
    fastapi_ok = test_fastapi_connection()
    
    # Test 2: Authentication
    print("\n2. Testing authentication...")
    user, auth_token = test_authentication()
    
    if not user or not auth_token:
        print("❌ Cannot proceed without authentication")
        return
    
    # Test 3: Learning Time Prediction (if FastAPI is available)
    if fastapi_ok:
        print("\n3. Testing learning time prediction...")
        prediction_ok = test_learning_time_prediction(user, auth_token)
        
        print("\n4. Testing Celery task...")
        celery_ok = test_celery_task(user, auth_token)
    else:
        print("\n3. Skipping learning time prediction test (FastAPI not available)")
        print("4. Skipping Celery task test (FastAPI not available)")
        prediction_ok = False
        celery_ok = False
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"FastAPI Connection: {'✓ PASS' if fastapi_ok else '❌ FAIL'}")
    print(f"Authentication: {'✓ PASS' if user and auth_token else '❌ FAIL'}")
    print(f"Learning Time Prediction: {'✓ PASS' if prediction_ok else '❌ FAIL'}")
    print(f"Celery Task: {'✓ PASS' if celery_ok else '❌ FAIL'}")
    
    if fastapi_ok and user and auth_token and (prediction_ok or celery_ok):
        print("\n🎉 Learning time prediction system is working!")
        print("\nTo ensure automatic predictions work:")
        print("1. Make sure both Django and FastAPI servers are running")
        print("2. Upload a new document")
        print("3. Wait for processing to complete")
        print("4. Check for automatic learning time prediction")
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        if not fastapi_ok:
            print("\nTo start FastAPI server:")
            print("cd llm_api && python main.py")

if __name__ == '__main__':
    main()
