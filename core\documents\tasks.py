from celery import shared_task
import aiohttp
import asyncio
from django.conf import settings
from .models import Document, DocumentEmbedding, BlueprintTopics, DocumentLearningTime
import logging

logger = logging.getLogger(__name__)

@shared_task(bind=True, max_retries=1, default_retry_delay=300)  # 5 minute delay before retry
def process_document_task(self, document_id: int, auth_token: str):
    """
    Celery task to process document embeddings in the background.
    Will retry once after 5 minutes if it fails.

    Sends the document to the FastAPI server for processing.
    """
    try:
        document = Document.objects.get(id=document_id)
        document.processing_status = 'processing'
        document.save()

        # Ensure we have a valid auth token
        if not auth_token:
            auth_token = f"user_{document.user.id}"
            logger.warning(f"No auth token provided for document {document_id}, using placeholder")

        # Create event loop for async FastAPI call
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        async def process():
            async with aiohttp.ClientSession() as session:
                url = f"{settings.FASTAPI_URL}/process-document/{document_id}"
                headers = {'Authorization': f'Bearer {auth_token}'}

                try:
                    # Open file and create form data
                    with open(document.file.path, 'rb') as f:
                        files = {'file': f}
                        async with session.post(url, data=files, headers=headers) as response:
                            if response.status != 200:
                                error_text = await response.text()
                                logger.error(f"FastAPI processing failed: {error_text}")
                                raise Exception(f"FastAPI processing failed: {error_text}")
                            return await response.json()
                except FileNotFoundError:
                    logger.error(f"File not found for document {document_id}: {document.file.path}")
                    document.processing_status = 'failed'
                    document.error_message = f"File not found: {document.file.name}"
                    document.save()
                    raise

        try:
            # Run async function and get result
            result = loop.run_until_complete(process())
            loop.close()

            # After successful document processing, trigger learning time prediction
            try:
                # Check if document processing was successful
                document.refresh_from_db()
                if document.processing_status == 'completed' and document.embeddings.exists():
                    # Trigger learning time prediction task
                    generate_learning_time_task.delay(document_id, auth_token, "gemini")
                    logger.info(f"Learning time prediction task triggered for document {document_id}")
            except Exception as e:
                # Don't fail the document processing if learning time prediction fails
                logger.warning(f"Failed to trigger learning time prediction for document {document_id}: {str(e)}")

            return result
        except Exception as e:
            logger.error(f"Error in async processing for document {document_id}: {str(e)}")
            # Update document status if the FastAPI server didn't do it
            document.refresh_from_db()
            if document.processing_status == 'processing':
                document.processing_status = 'failed'
                document.error_message = str(e)
                document.save()
            raise

    except Document.DoesNotExist:
        # Log error but don't retry - document was deleted
        logger.error(f"Document {document_id} not found")
        return {'error': f'Document {document_id} not found'}

    except Exception as exc:
        logger.error(f"Error processing document {document_id}: {str(exc)}")

        # Retry once after 5 minutes if we haven't retried yet
        if not self.request.retries:
            raise self.retry(exc=exc)
        else:
            # If this was our retry attempt, log the failure
            logger.error(f"Failed to process document {document_id} after retry: {str(exc)}")
            # Update document status if it's still in processing
            try:
                document = Document.objects.get(id=document_id)
                if document.processing_status == 'processing':
                    document.processing_status = 'failed'
                    document.error_message = f"Processing failed after retry: {str(exc)}"
                    document.save()
            except Document.DoesNotExist:
                pass
            raise

@shared_task(bind=True, max_retries=1, default_retry_delay=300)  # 5 minute delay before retry
def process_blueprint_task(self, document_id: int, auth_token: str, llm_model: str = "openai"):
    """
    Celery task to process blueprint topics in the background.
    Will retry once after 5 minutes if it fails.

    1. Sends the blueprint text to FastAPI for LLM processing
    2. LLM determines topics and their weightage
    3. Sets content pointers using RAG

    Parameters:
    - document_id: ID of the document to process
    - auth_token: Authentication token for the API
    - llm_model: LLM model to use ("openai" or "gemini")
    """
    try:
        document = Document.objects.get(id=document_id)

        # Skip if no blueprint
        if not document.blueprint:
            logger.warning(f"Document {document_id} has no blueprint")
            return {'error': f'Document {document_id} has no blueprint'}

        # Update status
        document.processing_status = 'processing'
        document.save()

        # Ensure we have a valid auth token
        if not auth_token:
            auth_token = f"user_{document.user.id}"
            logger.warning(f"No auth token provided for document {document_id}, using placeholder")

        # Create event loop for async FastAPI call
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        async def process():
            async with aiohttp.ClientSession() as session:
                url = f"{settings.FASTAPI_URL}/process-blueprint/{document_id}"
                headers = {'Authorization': f'Bearer {auth_token}'}

                # Send request to process the blueprint with the specified LLM model
                payload = {
                    'document_id': document_id,
                    'user_id': document.user.id,
                    'blueprint_text': document.blueprint,
                    'llm_model': llm_model
                }
                try:
                    async with session.post(url, json=payload, headers=headers) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(f"FastAPI blueprint processing failed: {error_text}")
                            raise Exception(f"FastAPI blueprint processing failed: {error_text}")
                        return await response.json()
                except Exception as e:
                    logger.error(f"Error in FastAPI request for blueprint {document_id}: {str(e)}")
                    raise

        try:
            # Run async function and get result
            result = loop.run_until_complete(process())
            loop.close()
            return result
        except Exception as e:
            logger.error(f"Error in async processing for blueprint {document_id}: {str(e)}")
            # Update document status if the FastAPI server didn't do it
            document.refresh_from_db()
            if document.processing_status == 'processing':
                document.processing_status = 'failed'
                document.error_message = str(e)
                document.save()
            raise

    except Document.DoesNotExist:
        # Log error but don't retry - document was deleted
        logger.error(f"Document {document_id} not found")
        return {'error': f'Document {document_id} not found'}

    except Exception as exc:
        logger.error(f"Error processing blueprint for document {document_id}: {str(exc)}")

        # Retry once after 5 minutes if we haven't retried yet
        if not self.request.retries:
            raise self.retry(exc=exc)
        else:
            # If this was our retry attempt, log the failure
            logger.error(f"Failed to process blueprint for document {document_id} after retry: {str(exc)}")
            # Update document status if it's still in processing
            try:
                document = Document.objects.get(id=document_id)
                if document.processing_status == 'processing':
                    document.processing_status = 'failed'
                    document.error_message = f"Blueprint processing failed after retry: {str(exc)}"
                    document.save()
            except Document.DoesNotExist:
                pass
            raise

@shared_task
def cleanup_failed_documents():
    """
    Periodic task to clean up failed documents and those stuck in processing
    after 24 hours
    """
    from django.utils import timezone
    from datetime import timedelta

    # Find documents that have been stuck in processing or failed for over 24 hours
    cutoff_time = timezone.now() - timedelta(hours=24)
    stuck_docs = Document.objects.filter(
        processing_status__in=['processing', 'failed'],
        uploaded_at__lt=cutoff_time
    )

    for doc in stuck_docs:
        # Delete the file
        if doc.file:
            try:
                doc.file.delete()
            except Exception:
                pass
        # Delete the document
        doc.delete()


@shared_task(bind=True, max_retries=1, default_retry_delay=300)  # 5 minute delay before retry
def generate_learning_time_task(self, document_id: int, auth_token: str, llm_model: str = "gemini"):
    """
    Celery task to generate learning time prediction in the background.
    Will retry once after 5 minutes if it fails.

    This task is automatically triggered after document processing is completed.

    Parameters:
    - document_id: ID of the document to generate learning time for
    - auth_token: Authentication token for the API
    - llm_model: LLM model to use ("gemini" recommended)
    """
    try:
        document = Document.objects.get(id=document_id)

        # Only generate if document processing is completed and has embeddings
        if document.processing_status != 'completed':
            logger.warning(f"Document {document_id} is not completed, skipping learning time generation")
            return {'error': f'Document {document_id} is not completed'}

        if not document.embeddings.exists():
            logger.warning(f"Document {document_id} has no embeddings, skipping learning time generation")
            return {'error': f'Document {document_id} has no embeddings'}

        # Check if learning time prediction already exists
        if DocumentLearningTime.objects.filter(document=document).exists():
            logger.info(f"Learning time prediction already exists for document {document_id}")
            return {'message': 'Learning time prediction already exists'}

        # Ensure we have a valid auth token
        if not auth_token:
            auth_token = f"user_{document.user.id}"
            logger.warning(f"No auth token provided for document {document_id}, using placeholder")

        # Create event loop for async FastAPI call
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        async def process():
            async with aiohttp.ClientSession() as session:
                url = f"{settings.FASTAPI_URL}/predict-learning-time/{document_id}"
                headers = {'Authorization': f'Bearer {auth_token}'}
                payload = {
                    'document_id': document_id,
                    'llm_model': llm_model,
                    'force_regenerate': False
                }
                try:
                    async with session.post(url, json=payload, headers=headers) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(f"FastAPI learning time prediction failed: {error_text}")
                            raise Exception(f"FastAPI learning time prediction failed: {error_text}")
                        return await response.json()
                except Exception as e:
                    logger.error(f"Error in FastAPI request for learning time {document_id}: {str(e)}")
                    raise

        try:
            # Run async function and get result
            result = loop.run_until_complete(process())
            loop.close()
            logger.info(f"Learning time prediction generated successfully for document {document_id}")
            return result
        except Exception as e:
            logger.error(f"Error in async processing for learning time {document_id}: {str(e)}")
            raise

    except Document.DoesNotExist:
        # Log error but don't retry - document was deleted
        logger.error(f"Document {document_id} not found")
        return {'error': f'Document {document_id} not found'}

    except Exception as exc:
        logger.error(f"Error generating learning time for document {document_id}: {str(exc)}")

        # Retry once after 5 minutes if we haven't retried yet
        if not self.request.retries:
            raise self.retry(exc=exc)
        else:
            # If this was our retry attempt, log the failure
            logger.error(f"Failed to generate learning time for document {document_id} after retry: {str(exc)}")
            raise